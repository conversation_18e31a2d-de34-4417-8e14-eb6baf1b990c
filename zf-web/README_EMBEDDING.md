# Frontend Embedding Integration

This document describes the frontend embedding feature that allows the backend Rust project to serve the frontend assets either from embedded data (production mode) or from the filesystem (development mode).

## Features

### Dual-Mode Operation

1. **Development Mode** (default): 
   - Frontend assets are served from the filesystem (`../zfc-web-ui/dist`)
   - Supports hot-reload and development workflow
   - No build-time embedding, faster compilation
   - Assets are served with `no-cache` headers

2. **Production Mode** (with `embed` feature):
   - Frontend assets are embedded into the Rust binary at compile time
   - Single binary deployment
   - Assets are served with long-term cache headers
   - Automatic frontend build during Rust compilation

## Usage

### Development Mode (Local Development)

```bash
# Build and run without embedding (default)
cargo run

# The backend will serve frontend files from ../zfc-web-ui/dist
# Make sure the frontend is built first:
cd ../zfc-web-ui && npm run build && cd ../zf-web
```

### Separate Deployment Mode (Backend-Only)

```bash
# Run backend without frontend serving
cargo run -- --disable-frontend-serving

# Or set environment variable
DISABLE_FRONTEND_SERVING=true cargo run

# Or specify a different frontend directory
cargo run -- --frontend-dir /path/to/frontend/dist
```

### Production Mode

```bash
# Build with embedded frontend assets
cargo build --release --features embed

# The resulting binary includes all frontend assets
# No need for separate frontend files
```

## Build Process

### Development Mode
- Frontend assets are served directly from the filesystem
- No automatic building - you need to run `npm run build` manually
- Changes to frontend files are immediately visible (after browser refresh)

### Production Mode
- The `build.rs` script automatically:
  1. Checks if frontend needs rebuilding
  2. Runs `npm install` if needed
  3. Runs `npm run build` to create the dist directory
  4. Embeds all files from `../zfc-web-ui/dist` into the binary

## API Compatibility

**All existing API endpoints remain unchanged:**
- `/api/*` routes are handled identically in both modes
- CORS configuration is preserved for cross-origin requests
- Authentication and authorization work exactly the same
- WebSocket connections (if any) are unaffected

## SPA Routing Support

Both modes support Single Page Application (SPA) routing:
- Static assets (CSS, JS, images) are served directly
- API routes (`/api/*`) are handled by the backend (unchanged)
- Download routes (`/downloads/*`) are handled by the backend (unchanged)
- All other routes fallback to `index.html` for client-side routing
- Frontend routing works identically in both deployment modes

## MIME Type Handling

Proper MIME types are automatically detected and set for:
- HTML files (`text/html`)
- CSS files (`text/css`)
- JavaScript files (`application/javascript`)
- Images (`image/*`)
- Other file types based on extension

## Caching Strategy

### Development Mode
- `Cache-Control: no-cache` for all assets
- Supports development workflow with immediate updates

### Production Mode
- `Cache-Control: public, max-age=********` for static assets (1 year)
- `Cache-Control: no-cache` for `index.html` (SPA fallback)

## Deployment Compatibility

**IMPORTANT: Zero Breaking Changes**

The embedding feature is completely optional and maintains full backward compatibility:

### Existing Deployment (Unchanged)
- The existing deployment script (`../zfc-web-ui/deploy.sh`) works exactly as before
- Frontend and backend can still be deployed to separate servers
- All API endpoints and CORS configurations remain unchanged
- No modifications needed to existing deployment workflows

### Deployment Scenarios

#### Scenario A: Separate Servers (Current/Default)
```bash
# Frontend (on server A)
cd zfc-web-ui
./deploy.sh http://backend-server:3030/api

# Backend (on server B)
cd zf-web
cargo build --release
./target/release/zf-web
```

#### Scenario B: Single Server with Embedded Frontend (New)
```bash
# Single binary with embedded frontend
cd zf-web
cargo build --release --features embed
./target/release/zf-web
# Frontend now served at http://localhost:3030/
```

### When to Use Each Approach

**Use Separate Deployment When:**
- Deploying to different servers/containers
- Need independent scaling of frontend/backend
- Using CDN for frontend assets
- Different teams manage frontend/backend
- Existing deployment pipeline works well

**Use Embedded Deployment When:**
- Single server deployment
- Simplified deployment process desired
- Reduced infrastructure complexity
- Edge computing or resource-constrained environments
- Single binary distribution preferred

## File Structure

```
zf-web/
├── Cargo.toml          # Added embed feature and dependencies
├── build.rs            # Automatic frontend building for embed mode
├── src/main.rs         # Frontend serving routes
└── README_EMBEDDING.md # This documentation

../zfc-web-ui/
├── dist/               # Frontend build output (served in dev mode)
├── deploy.sh           # Docker deployment (unchanged)
└── ...                 # Frontend source files
```

## Configuration

### Cargo.toml Features
```toml
[features]
default = []
embed = ["warp-embed", "rust-embed"]
```

### Dependencies
- `warp-embed`: Serves embedded files with warp
- `rust-embed`: Embeds files at compile time
- `mime_guess`: MIME type detection

## Error Handling

- Missing frontend files in development mode return 404
- Build failures in production mode cause compilation to fail
- Invalid paths are rejected for security
- SPA fallback ensures frontend routing works correctly

## Security

- Path traversal protection in filesystem mode
- Only files within the designated frontend directory are served
- API and download routes are properly isolated
