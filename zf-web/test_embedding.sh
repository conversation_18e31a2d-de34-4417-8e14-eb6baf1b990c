#!/bin/bash

# Test script for frontend embedding functionality
# This script tests both development and production modes

set -e

echo "=== Frontend Embedding Test ==="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if frontend is built
print_status "Checking frontend build..."
if [ ! -d "../zfc-web-ui/dist" ]; then
    print_warning "Frontend not built. Building now..."
    cd ../zfc-web-ui
    npm run build
    cd ../zf-web
    print_status "Frontend build completed"
else
    print_status "Frontend build found"
fi

# Test 1: Development mode compilation
print_status "Testing development mode compilation..."
if cargo check; then
    print_status "✓ Development mode compilation successful"
else
    print_error "✗ Development mode compilation failed"
    exit 1
fi

# Test 2: Production mode compilation
print_status "Testing production mode compilation..."
if cargo check --features embed; then
    print_status "✓ Production mode compilation successful"
else
    print_error "✗ Production mode compilation failed"
    exit 1
fi

# Test 3: Build script functionality
print_status "Testing build script..."
if cargo build --features embed --quiet; then
    print_status "✓ Build with embed feature successful"
else
    print_error "✗ Build with embed feature failed"
    exit 1
fi

# Test 4: Check if binary was created (workspace structure)
BINARY_PATH="../target/debug/zf-web"
if [ -f "$BINARY_PATH" ]; then
    print_status "✓ Binary created successfully"

    # Get binary size
    BINARY_SIZE=$(du -h "$BINARY_PATH" | cut -f1)
    print_status "Binary size: $BINARY_SIZE"
else
    print_error "✗ Binary not found at $BINARY_PATH"
    exit 1
fi

# Test 5: Verify embedded assets (basic check)
print_status "Checking if assets are embedded..."
if strings "$BINARY_PATH" | grep -q "index.html\|\.css\|\.js"; then
    print_status "✓ Assets appear to be embedded in binary"
else
    print_warning "? Could not verify embedded assets (this might be normal)"
fi

# Test 6: Test separate deployment mode (backend-only)
print_status "Testing separate deployment mode..."
if cargo check --quiet; then
    print_status "✓ Backend-only mode compilation successful"
else
    print_error "✗ Backend-only mode compilation failed"
    exit 1
fi

# Test 7: Test configuration options
print_status "Testing configuration options..."
if cargo run --quiet -- --help | grep -q "disable-frontend-serving\|frontend-dir"; then
    print_status "✓ Configuration options available"
else
    print_warning "? Could not verify configuration options"
fi

echo
print_status "=== All tests passed! ==="
echo
print_status "Usage:"
echo "  Development mode: cargo run"
echo "  Production mode:  cargo run --features embed"
echo
print_status "The backend will serve the frontend at http://localhost:3030"
print_status "API endpoints remain at http://localhost:3030/api/*"
echo
print_status "Deployment options:"
echo "  1. Docker (existing): Use ../zfc-web-ui/deploy.sh"
echo "  2. Single binary: Use 'cargo build --release --features embed'"
