use std::{
    collections::{HashMap, HashSet},
    fmt::{Debug, Display},
    hash::Hash,
    net::{IpAddr, SocketAddr},
    sync::Arc,
    time::Duration,
};

use common::app_message::{self, PortConfig};
use dashmap::DashMap;
use futures::{future::BoxFuture, stream, StreamExt};
use log::{error, info};
use private_tun::{runtime_provider::RuntimeProvider, self_proxy::MemDuplex};
use tokio::{net::TcpStream, sync::Mutex};
use tokio_util::sync::CancellationToken;

#[cfg(feature = "audit_log")]
use crate::log_reporter::LogClient;
use crate::{
    collector_factory::CollectorFactory, conn_limitter::ConnLimitter, preludes::FilterType,
    types::TargetData, PingClient,
};

pub mod hammer;
pub mod tot;
#[async_trait::async_trait]
pub trait ProtocolHandler: Send + Sync + 'static {
    async fn proc_tcp_stream(
        &self,
        stream: TcpStream,
        cancel: CancellationToken,
        filters: Option<Arc<Vec<FilterType>>>,
        out_ip_addr: Option<IpAddr>,
    ) -> anyhow::Result<()>;
    async fn proc_udp_bind(
        &self,
        bind_addr: Option<SocketAddr>,
        out_addr: Option<IpAddr>,
        cancel: CancellationToken,
        #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
        sub_id: i32,
        conn_limitter: Option<ConnLimitter>,
    ) -> anyhow::Result<()>;
}
pub struct CustomPipedStream {
    pub io: MemDuplex,
}
type PipedStreamCreator = Arc<
    Box<dyn Fn() -> BoxFuture<'static, anyhow::Result<CustomPipedStream>> + Send + Sync + 'static>,
>;
#[async_trait::async_trait]
pub trait ProtocolHandlerFactory {
    type Protocol: Protocol;
    async fn create_protocol_handler(
        &self,
        bind_port: u16,
        target: TargetData,
        collector_factory: Arc<CollectorFactory>,
        ping_client: Arc<PingClient>,
        port_cancel: CancellationToken,
        piped_stream_creator: Option<PipedStreamCreator>,
        rt_provider: Arc<RuntimeProvider>,
    ) -> anyhow::Result<Arc<Box<dyn ProtocolHandler>>>;
}

#[async_trait::async_trait]
impl<T: ProtocolHandlerFactory + Send + Sync + 'static> ProtocolHandlerFactory for Arc<T> {
    type Protocol = T::Protocol;
    async fn create_protocol_handler(
        &self,
        bind_port: u16,
        target: TargetData,
        collector_factory: Arc<CollectorFactory>,
        ping_client: Arc<PingClient>,
        port_cancel: CancellationToken,
        piped_stream_creator: Option<PipedStreamCreator>,
        rt_provider: Arc<RuntimeProvider>,
    ) -> anyhow::Result<Arc<Box<dyn ProtocolHandler>>> {
        self.as_ref()
            .create_protocol_handler(
                bind_port,
                target,
                collector_factory,
                ping_client,
                port_cancel,
                piped_stream_creator,
                rt_provider,
            )
            .await
    }
}

#[async_trait::async_trait]
pub trait Protocol: Sized {
    type Config: for<'a> TryFrom<&'a app_message::Protocol> + Debug;
    // 用来区分不同的client，因为可能存在多个port使用同一个client的情况，并且每个client的资源消耗很大，所以必须要尽可能复用client
    type Id: Hash
        + Eq
        + Display
        + Clone
        + Send
        + Sync
        + 'static
        + for<'a> From<&'a Self::Config>
        + Debug;
    type Client: 'static + Send + Sync + ProtocolHandlerFactory<Protocol = Self> + Clone;
    type Ctx: 'static + Send + Sync + Clone;
    const NEED_PORT: bool;
    fn init_ctx(&self, rt_provider: Arc<RuntimeProvider>) -> Self::Ctx;
    async fn start(
        ctx: &Self::Ctx,
        manager: &ProtocolManager<Self>,
        id: &Self::Id,
        config: &Self::Config,
    ) -> anyhow::Result<Self::Client>;
    async fn stop(ctx: &Self::Ctx, client: Self::Client) -> anyhow::Result<()>;
    async fn clear_used_port(
        ctx: &Self::Ctx,
        manager: &ProtocolManager<Self>,
        config: &Self::Config,
    ) -> Option<u16>;
    fn config_from_client<'a>(ctx: &Self::Ctx, client: &'a Self::Client) -> &'a Self::Config;
}

#[allow(unused)]
#[derive(Clone)]
struct UsedPortRecorder {
    port_range: (u16, u16),
    used_ports: Arc<Mutex<HashSet<u16>>>,
}
#[allow(unused)]
impl UsedPortRecorder {
    pub fn new(port_range: (u16, u16)) -> Self {
        Self {
            port_range,
            used_ports: Arc::new(Mutex::new(HashSet::new())),
        }
    }
    pub async fn get_one_used_port_in_range(&self) -> anyhow::Result<u16> {
        let (start, end) = self.port_range;
        let mut lock = self.used_ports.lock().await;
        for port in start..end {
            if !lock.contains(&port) {
                lock.insert(port);
                return Ok(port);
            }
        }
        Err(anyhow::anyhow!(
            "no available port in range: {} - {}",
            start,
            end
        ))
    }
    pub async fn mark_port_unused(&self, port: u16) {
        let mut lock = self.used_ports.lock().await;
        lock.remove(&port);
    }
}
#[derive(Clone)]
pub struct ProtocolManager<T: Protocol> {
    clients: Arc<DashMap<T::Id, T::Client>>,
    port_id_map: Arc<std::sync::Mutex<HashMap<u16, T::Id>>>,
    #[allow(unused)]
    used_port_recorder: Option<UsedPortRecorder>,
    ctx: T::Ctx,
}

impl<T: Protocol> ProtocolManager<T> {
    pub fn new(port_range: Option<(u16, u16)>, ctx: T::Ctx) -> Self {
        Self {
            clients: Arc::new(DashMap::new()),
            port_id_map: Arc::new(std::sync::Mutex::new(HashMap::new())),
            used_port_recorder: if T::NEED_PORT {
                port_range.map(|x| UsedPortRecorder::new(x))
            } else {
                None
            },
            ctx,
        }
    }
    pub fn ctx(&self) -> &T::Ctx {
        &self.ctx
    }
    #[allow(unused)]
    pub async fn get_one_used_port_in_range(&self) -> anyhow::Result<u16> {
        if let Some(recorder) = &self.used_port_recorder {
            recorder.get_one_used_port_in_range().await
        } else {
            unreachable!("should not call this function when T::NEED_PORT is false")
        }
    }
    #[allow(unused)]
    pub async fn mark_port_unused(&self, port: u16) {
        if let Some(recorder) = &self.used_port_recorder {
            recorder.mark_port_unused(port).await;
        }
    }

    pub async fn update_clients(
        &self,
        remote_configs: HashMap<&T::Id, &app_message::Protocol>,
    ) -> anyhow::Result<HashSet<T::Id>> {
        // remove all unused hammer clients
        let mut should_stop_clients = smallvec::SmallVec::<[T::Id; 4]>::new();
        let mut removed_clients = HashSet::new();
        for x in self.clients.iter() {
            let hammer_ident = x.key();
            if !remote_configs.contains_key(hammer_ident) {
                info!("client {} no more used", hammer_ident);
                should_stop_clients.push(hammer_ident.clone());
                removed_clients.insert(hammer_ident.clone());
            }
        }
        let mut added_clients = HashSet::new();
        let mut should_start_clients = smallvec::SmallVec::<[T::Id; 4]>::new();
        // add new hammer clients
        for x in remote_configs.iter() {
            let hammer_ident = x.0;
            if !self.clients.contains_key(hammer_ident) {
                info!("add new client: {}", hammer_ident);
                should_start_clients.push((*hammer_ident).clone());
                added_clients.insert((*hammer_ident).clone());
            }
        }

        // detect modify on config
        let modified_clients = added_clients
            .union(&removed_clients)
            .map(|x| x.clone())
            .collect::<HashSet<_>>();

        // do action
        stream::iter(should_stop_clients.into_iter())
            .for_each_concurrent(8, |id| {
                let to_removed = self.clients.remove(&id).map(|x| x.1);
                async move {
                    if let Some(client) = to_removed {
                        let config = T::config_from_client(&self.ctx, &client);
                        T::clear_used_port(&self.ctx, self, config).await;
                        info!("stop client: {}", id);
                        match tokio::time::timeout(
                            Duration::from_secs(5),
                            T::stop(&self.ctx, client),
                        )
                        .await
                        {
                            Ok(rst) => {
                                if let Err(e) = rst {
                                    error!("stop client: {} failed: {}", id, e);
                                }
                            }
                            Err(_) => {
                                error!("stop client: {} failed: timeout", id);
                            }
                        }
                    }
                }
            })
            .await;

        stream::iter(should_start_clients.into_iter())
            .for_each_concurrent(8, |id| {
                let cfg = remote_configs
                    .get(&id)
                    .and_then(|x| T::Config::try_from(x).ok());
                async {
                    if let Some(cfg) = cfg {
                        info!("start new client: {}", id);
                        match tokio::time::timeout(
                            Duration::from_secs(30), // given 60s to start client
                            T::start(&self.ctx, self, &id, &cfg),
                        )
                        .await
                        {
                            Ok(Ok(client)) => {
                                info!("start client: {} success", id);
                                self.clients.insert(id, client);
                            }
                            Ok(Err(e)) => {
                                error!("start client: {} failed: {}", id, e);
                            }
                            Err(_) => {
                                error!("start client: {} failed: timeout", id);
                            }
                        }
                    }
                }
            })
            .await;
        Ok(modified_clients)
    }

    pub async fn process_port_map(
        &self,
        remote_map: &HashMap<u16, PortConfig>,
    ) -> anyhow::Result<PortChecker> {
        let mapper = remote_map
            .iter()
            .filter_map(|(port, cfg)| {
                // current only support hammer
                if let Some(protocol) = cfg.protocol.as_ref() {
                    if let Ok(id) = T::Config::try_from(protocol).map(|x| T::Id::from(&x)) {
                        Some((*port, (id, protocol)))
                    } else {
                        None
                    }
                } else {
                    None
                }
            })
            .collect::<HashMap<_, _>>();
        let new_port_id_map = mapper
            .iter()
            .map(|x| (*x.0, x.1 .0.clone()))
            .collect::<HashMap<_, _>>();
        let mut modified_ports = HashSet::new();
        {
            let mut old_port_id_map = self.port_id_map.lock().unwrap();
            // detect for removed hammer map
            for (port, old_id) in old_port_id_map.iter() {
                match new_port_id_map.get(port) {
                    Some(new_id) => {
                        if old_id != new_id {
                            info!("hammer port {} config changed", port);
                            modified_ports.insert(*port);
                        }
                    }
                    None => {
                        info!("hammer port {} no more used", port);
                        modified_ports.insert(*port);
                    }
                }
            }
            // detect for added hammer map
            for (port, new_id) in new_port_id_map.iter() {
                match old_port_id_map.get(port) {
                    Some(old_id) => {
                        if old_id != new_id {
                            info!("hammer port {} config changed", port);
                            modified_ports.insert(*port);
                        }
                    }
                    None => {
                        info!("hammer port {} is new", port);
                        modified_ports.insert(*port);
                    }
                }
            }
            *old_port_id_map = new_port_id_map;
        }
        // log::debug!("remote_port_config: {:?}", remote_port_config);
        let remote_hammer_configs = mapper
            .iter()
            .map(|x| (&x.1 .0, x.1 .1))
            .collect::<HashMap<_, _>>();
        log::debug!("remote_hammer_configs: {:?}", remote_hammer_configs);
        let modified_clients = match self.update_clients(remote_hammer_configs).await {
            Ok(p) => p,
            Err(e) => {
                error!("update hammer servers failed: {}", e);
                HashSet::new()
            }
        };
        modified_ports.extend(
            self.port_id_map
                .lock()
                .unwrap()
                .iter()
                .filter(|(_port, id)| modified_clients.contains(id))
                .map(|x| *x.0),
        );
        Ok(PortChecker {
            modified_clients: modified_ports,
        })
    }
    pub fn is_my_port(&self, port: u16) -> bool {
        self.port_id_map.lock().unwrap().contains_key(&port)
    }
    pub async fn create_protocol_handler(
        &self,
        port: u16,
        target: TargetData,
        collector_factory: Arc<CollectorFactory>,
        ping_client: Arc<PingClient>,
        port_cancel: CancellationToken,
        rt_provider: Arc<RuntimeProvider>,
    ) -> anyhow::Result<Option<Arc<Box<dyn ProtocolHandler>>>> {
        let task = {
            let port_lock = self.port_id_map.lock().unwrap();
            let id = port_lock
                .get(&port)
                .ok_or(anyhow::anyhow!("port: {} not found", port))?;
            let task = self
                .clients
                .get(id)
                .map(|c| {
                    let c = c.clone();
                    async move {
                        let handler = c
                            .create_protocol_handler(
                                port,
                                target,
                                collector_factory,
                                ping_client,
                                port_cancel,
                                None,
                                rt_provider,
                            )
                            .await?;
                        Ok::<_, anyhow::Error>(handler)
                    }
                })
                .ok_or(anyhow::anyhow!("client: {} not found", id))?;
            task
        };
        match task.await {
            Ok(handler) => {
                log::info!("create_protocol_handler for port: {}", port);
                Ok(Some(handler))
            }
            Err(e) => {
                error!("create_protocol_handler for port: {} failed: {}", port, e);
                Ok(None)
            }
        }
    }
}

pub struct PortChecker {
    modified_clients: HashSet<u16>, // 有哪些涉及到的port需要重建
}

impl PortChecker {
    pub fn is_port_modified(&self, port: u16) -> bool {
        self.modified_clients.contains(&port)
    }
}
