use crate::collector_factory::{CollectorFactory, Handle};
use crate::conn_limitter::{Conn<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Conn<PERSON>imitter, Guard};
#[cfg(feature = "audit_log")]
use crate::log_reporter::LogClient;
use crate::protocol::ProtocolHandler;
use crate::server_provider::{create_server_selector_direct, ServerSelector};
use crate::udp::{proc_udp_bind, BindUdp, <PERSON>Udp<PERSON><PERSON><PERSON>, Socks5UdpHandler};
use crate::zero_copy_wrapper::ZeroCopyWrapper;
use crate::{preludes::*, types::TargetData};
use common::constant::{TCP_CONNECT_TIMEOUT, TCP_FAST_CONNECT_TIMEOUT};
use common::server_provider::{ServerInfo, ServerProvider};
use private_tun::snell_impl_ver::traffic_filter::TrafficFilter;
use realm_io::CopyBuffer;
use smallvec::smallvec;
use socket2::TcpKeepalive;
use socks5_impl::{
    client::{self},
    protocol::UserKey,
};
use std::net::SocketAddr;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use tokio::io::{AsyncRead, AsyncWrite, AsyncWriteExt};
use tokio::{net::TcpStream, select};
use tokio_util::sync::CancellationToken;

#[derive(Debug, Clone)]
pub struct SocksBackend {
    auth: Option<UserKey>,
    socks5_server: SocketAddr,
}

impl SocksBackend {
    pub fn new(socks5_server: SocketAddr, auth: Option<UserKey>) -> Self {
        Self {
            socks5_server,
            auth,
        }
    }
    pub fn proxy_url(&self) -> String {
        if let Some(auth) = self.auth.as_ref() {
            format!(
                "socks5h://{}:{}@{}",
                auth.username, auth.password, self.socks5_server
            )
        } else {
            format!("socks5h://{}", self.socks5_server)
        }
    }
}
#[derive(thiserror::Error, Debug)]
enum RealyTcpError {
    #[error("socks5 connect failed")]
    Socks5ConnectFailed,
    #[error("socks5 connect timeout")]
    Socks5ConnectTimeout,
    #[error("socks5 handle failed")]
    Socks5HandleFailed(#[from] socks5_impl::Error),
    #[error("remote connect failed")]
    RemoteConnectFailed(#[from] std::io::Error),
    #[error("get target info failed")]
    GetTargetInfoFailed(#[from] anyhow::Error),
}
async fn connect_to_remote(
    server_selector: &ServerSelector,
    socks5_server: &Option<Arc<SocksBackend>>,
    out_ip_addr: Option<IpAddr>,
) -> Result<TcpStream, RealyTcpError> {
    let mut black_server_list = smallvec::SmallVec::<[Arc<ServerInfo>; 3]>::new();
    for i in 0..3 {
        let target = server_selector
            .get_target_info(Some(black_server_list.as_slice()))
            .map_err(RealyTcpError::GetTargetInfoFailed)?;
        let target_port = target.port();
        let target_addr = target.socket_addr();
        if let Some(SocksBackend {
            auth,
            socks5_server,
        }) = socks5_server.as_deref()
        {
            let mut stream = tokio::time::timeout(
                Duration::from_secs(TCP_CONNECT_TIMEOUT),
                TcpStream::connect(socks5_server.clone()),
            )
            .await
            .map_err(|_| RealyTcpError::Socks5ConnectTimeout)?
            .map_err(|_| RealyTcpError::Socks5ConnectFailed)?;
            let addr = tokio::time::timeout(
                Duration::from_secs(TCP_CONNECT_TIMEOUT),
                client::connect(
                    &mut stream,
                    (target.socket_addr().ip(), target_port),
                    auth.clone(),
                ),
            )
            .await
            .map_err(|_| RealyTcpError::Socks5ConnectTimeout)?
            .map_err(|e| RealyTcpError::Socks5HandleFailed(e))?;
            debug!("socks5 connect success: {addr:?}, target: {target}, port: {target_port}");
            return Ok(stream);
        } else {
            let stream = if let Some(out_ip_addr) = out_ip_addr.as_ref() {
                match out_ip_addr {
                    IpAddr::V4(_v4) if target_addr.ip().is_ipv4() => {
                        let socket = tokio::net::TcpSocket::new_v4()?;
                        tokio::time::timeout(
                            Duration::from_secs(TCP_FAST_CONNECT_TIMEOUT * (i + 1)),
                            socket.connect(target_addr.clone()),
                        )
                        .await
                    }
                    IpAddr::V6(_v6) if target_addr.ip().is_ipv6() => {
                        let socket = tokio::net::TcpSocket::new_v6()?;
                        tokio::time::timeout(
                            Duration::from_secs(TCP_FAST_CONNECT_TIMEOUT * (i + 1)),
                            socket.connect(target_addr.clone()),
                        )
                        .await
                    }
                    // fallback to connect to target ip family
                    _ => {
                        tokio::time::timeout(
                            Duration::from_secs(TCP_FAST_CONNECT_TIMEOUT * (i + 1)),
                            TcpStream::connect(target_addr),
                        )
                        .await
                    }
                }
            } else {
                tokio::time::timeout(
                    Duration::from_secs(TCP_FAST_CONNECT_TIMEOUT * (i + 1)),
                    TcpStream::connect(target_addr),
                )
                .await
            };
            match stream {
                Ok(Ok(v)) => return Ok(v),
                Ok(Err(e)) => {
                    if i == 0 {
                        error!("connect to target: {target} failed: {e} retry after 50ms");
                        server_selector.recheck();
                    }
                }
                Err(_) => {
                    if i == 0 {
                        error!("connect to target: {target} failed: timeout retry after 50ms");
                        server_selector.recheck();
                    }
                }
            }
            black_server_list.push(target);
            // tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        }
    }
    Err(RealyTcpError::RemoteConnectFailed(std::io::Error::new(
        std::io::ErrorKind::Other,
        "connect to target failed",
    )))
}

async fn start_tcp_relay<T: AsyncRead + AsyncWrite + Send + Sync + 'static + Unpin>(
    mut client_stream: T,
    port: u16,
    server_selector: ServerSelector,
    socks5_server: Option<Arc<SocksBackend>>,
    collector: Handle,
    cancel: CancellationToken,
    incoming_addr: SocketAddr,
    out_ip_addr: Option<IpAddr>,
    _guard: Option<Guard>,
) -> Result<(), RealyTcpError> {
    let conn = select! {
        biased;
        r = connect_to_remote(&server_selector, &socks5_server, out_ip_addr) => {
            r
        }
        _ = cancel.cancelled() => {
            log::debug!("TCP#{} <- {:?}: canceled on connect", port, &incoming_addr);
            let _ = client_stream.shutdown().await;
            return Err(RealyTcpError::RemoteConnectFailed(std::io::Error::new(std::io::ErrorKind::Other, "canceled")));
        }
    };
    let stream = match conn {
        Ok(v) => v,
        Err(e) => {
            if matches!(e, RealyTcpError::RemoteConnectFailed(_)) {
                server_selector.recheck();
            }
            let _ = client_stream.shutdown().await;
            return Err(e);
        }
    };
    let _ = stream.set_nodelay(true);
    {
        let socket_ref = socket2::SockRef::from(&stream);
        #[cfg(target_os = "linux")]
        let _ = socket_ref.set_tcp_user_timeout(Some(std::time::Duration::from_secs(302)));
        let _ = socket_ref.set_tcp_keepalive(
            &TcpKeepalive::new()
                .with_time(std::time::Duration::from_secs(300))
                .with_interval(std::time::Duration::from_secs(2))
                .with_retries(1),
        );
    }
    let mut local = ZeroCopyWrapper::new(client_stream, Some(collector.collector.clone()));
    let mut remote = ZeroCopyWrapper::new(stream, None);
    let buf1 = CopyBuffer::new(vec![0u8; 4096]);
    let buf2 = CopyBuffer::new(vec![0u8; 8192]);
    select! {
        biased;
        r = realm_io::bidi_copy_buf(&mut local, &mut remote, buf1, buf2) => {
            if let Err(e) = r {
                match e.kind() {
                    std::io::ErrorKind::TimedOut | std::io::ErrorKind::ConnectionReset | std::io::ErrorKind::ConnectionRefused => {
                        // ignore
                    }
                    e => {
                        error!("TCP#{} <- {:?}: {:?}", port, &incoming_addr, e);
                    }
                }
            }
        }
        _ = cancel.cancelled() => {
            debug!("TCP#{} <- {:?}: canceled", port, &incoming_addr);
        }

    };
    let _ = local.shutdown().await;
    let _ = remote.shutdown().await;
    Ok(())
}

async fn proxy_socks5_udp(
    server: SocketAddr, // remote socks5 server
    bind: SocketAddr,   // listen
    target: ServerSelector,
    auth: &Option<UserKey>,
    collector_factory: Arc<CollectorFactory>,
    cancel: CancellationToken,
    #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
    sub_id: i32,
    conn_limitter: Option<ConnLimitter>,
) -> anyhow::Result<()> {
    let udp_handler = Socks5UdpHandler::new(server, auth.clone(), conn_limitter);
    let port = bind.port();
    let bind = BindUdp::SocketAddr(bind);
    proc_udp_bind(
        bind,
        &target,
        cancel,
        udp_handler,
        collector_factory,
        #[cfg(feature = "audit_log")]
        log_client,
        sub_id,
    )
    .await?;
    log::info!("socks5 udp proxy, server: {server} bind port: {port} end");
    Ok(())
}

async fn proxy_direct_udp(
    bind: SocketAddr, // listen
    out_addr: Option<IpAddr>,
    support_ipv6: bool,
    target: ServerSelector,
    collector_factory: Arc<CollectorFactory>,
    cancel: CancellationToken,
    #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
    sub_id: i32,
    conn_limitter: Option<ConnLimitter>,
) -> anyhow::Result<()> {
    let udp_handler = DirectUdpHandler::new(
        out_addr.unwrap_or(if support_ipv6 {
            Ipv6Addr::UNSPECIFIED.into()
        } else {
            Ipv4Addr::UNSPECIFIED.into()
        }),
        conn_limitter,
    );
    let port = bind.port();
    let bind = BindUdp::SocketAddr(bind);
    proc_udp_bind(
        bind,
        &target,
        cancel,
        udp_handler,
        collector_factory,
        #[cfg(feature = "audit_log")]
        log_client,
        sub_id,
    )
    .await?;
    log::info!("direct udp proxy, bind port: {port} end");
    Ok(())
}

pub async fn relay_udp_via_socks5_or_direct(
    local_port: u16,
    bind_addr: Option<SocketAddr>,
    out_addr: Option<IpAddr>,
    server_selector: ServerSelector,
    socks5_server: &Option<Arc<SocksBackend>>,
    cancel: CancellationToken,
    collector_factory: Arc<CollectorFactory>,
    #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
    sub_id: i32,
    conn_limitter: Option<ConnLimitter>,
) -> anyhow::Result<()> {
    let support_ipv6 = is_ipv6_supported();
    let bind_addr = if support_ipv6 {
        bind_addr.unwrap_or((Ipv6Addr::UNSPECIFIED, local_port).into())
    } else {
        bind_addr.unwrap_or((Ipv4Addr::UNSPECIFIED, local_port).into())
    };
    if let Some(SocksBackend {
        auth,
        socks5_server,
    }) = socks5_server.as_deref()
    {
        proxy_socks5_udp(
            socks5_server.clone(),
            bind_addr,
            server_selector,
            auth,
            collector_factory,
            cancel,
            #[cfg(feature = "audit_log")]
            log_client,
            sub_id,
            conn_limitter,
        )
        .await?;
    } else {
        proxy_direct_udp(
            bind_addr,
            out_addr,
            support_ipv6,
            server_selector,
            collector_factory,
            cancel,
            #[cfg(feature = "audit_log")]
            log_client,
            sub_id,
            conn_limitter,
        )
        .await?;
    };

    Ok(())
}

pub fn create_conn_limitter(target: &Arc<TargetData>) -> Option<ConnLimitter> {
    if target.allow_conn_num.is_some() || target.allow_ip_num.is_some() {
        Some(ConnLimitter::new(
            target
                .allow_ip_num
                .iter()
                .filter_map(|v| if *v < 0 { None } else { Some(*v as usize) })
                .next(),
            target
                .allow_conn_num
                .iter()
                .filter_map(|v| if *v < 0 { None } else { Some(*v as usize) })
                .next(),
        ))
    } else {
        None
    }
}

pub async fn listen_tcp(
    port: u16,
    bind_addr: Option<SocketAddr>,
    target: Arc<TargetData>,
    socks_backend: Option<Arc<SocksBackend>>,
    forward_handler: Option<Arc<Box<dyn ProtocolHandler>>>, // current only using hammer protocol
    ping_client: Arc<PingClient>,
    cancel: CancellationToken,
    collector_factory: Arc<CollectorFactory>,
    #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
    filters: Option<Arc<Vec<FilterType>>>,
    out_ip_addr: Option<IpAddr>,
) -> Result<()> {
    let support_ipv6 = is_ipv6_supported();
    let bind_addr = if support_ipv6 {
        bind_addr.unwrap_or((Ipv6Addr::UNSPECIFIED, port).into())
    } else {
        bind_addr.unwrap_or((Ipv4Addr::UNSPECIFIED, port).into())
    };
    let mut try_bind_cnt = 0;
    let listener = match create_tcp_listener(bind_addr) {
        Ok(v) => v,
        Err(_) => loop {
            if cancel.is_cancelled() {
                return Err(anyhow::anyhow!("port: {} bind failed: canceled", port));
            }
            match create_tcp_listener(bind_addr) {
                Ok(v) => {
                    break v;
                }
                Err(e) => {
                    error!("port: {} bind failed: {e} try bind again after 5s", port);
                    if try_bind_cnt > 10 {
                        return Err(e.into());
                    }
                    try_bind_cnt += 1;
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        },
    };
    log::debug!("port: {} create tcp listener success", port);
    // create direct server selector only when forward_handler is not set
    let server_selector = if forward_handler.is_none() {
        let mode = target.mode;
        match tokio::time::timeout(
            Duration::from_secs(20),
            create_server_selector_direct(
                target.as_ref().into(),
                mode,
                target.latency_test_method,
                cancel.clone(),
                ping_client,
            ),
        )
        .await
        {
            Ok(Ok(v)) => Some(v),
            Ok(Err(e)) => {
                error!("port: {} create server provider failed: {e}", port);
                return Err(e);
            }
            Err(_) => {
                error!("port: {} create server provider timeout", port);
                return Err(anyhow::anyhow!(
                    "port: {} create server provider timeout",
                    port
                ));
            }
        }
    } else {
        None
    };
    log::debug!("port: {} create server selector success", port);
    let mut failed_cnt = 0;
    let conn_limitter = create_conn_limitter(&target);
    loop {
        let r = select! {
            biased;
            r = listener.accept() => {
                r
            }
            _ = cancel.cancelled() => {
                log::info!("tcp listen on port: {} canceled", port);
                return Ok(());
            }
        };
        let (stream, incoming_addr) = match r {
            Ok(v) => v,
            Err(e) => {
                error!(
                    "Accept on port {} failed: {:?} failed_cnt: {}",
                    port, e, failed_cnt
                );
                failed_cnt += 1;
                if failed_cnt > 10 {
                    return Err(e.into());
                }
                #[cfg(not(target_os = "windows"))]
                if let Some(24) | Some(23) = e.raw_os_error() {
                    tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
                }
                continue;
            }
        };
        let guard = if let Some(conn_limitter) = conn_limitter.as_ref() {
            match conn_limitter.add_conn(incoming_addr) {
                ConnLimitResult::Ok(guard) => Some(guard),
                ConnLimitResult::IpLimit {
                    allow_ip_num,
                    current_ip_num,
                } => {
                    log::warn!(
                    "TCP#{} <- {:?} connection limit reached limit ip num: {:?}, current ip num: {:?}",
                    port,
                    &incoming_addr,
                    allow_ip_num,
                    current_ip_num
                );
                    continue;
                }
                ConnLimitResult::ConnLimit {
                    allow_conn_num,
                    current_conn_num,
                } => {
                    log::warn!(
                    "TCP#{} <- {:?} connection limit reached limit conn num: {:?}, current conn num: {:?}",
                    port,
                    &incoming_addr,
                    allow_conn_num,
                    current_conn_num
                );
                    continue;
                }
            }
        } else {
            None
        };
        failed_cnt = 0;
        debug!(
            "Accepting TCP connection on port {} from {:?} sub_id: {:?}",
            port, &incoming_addr, target.sub_id
        );
        #[cfg(feature = "audit_log")]
        let _ = log_client.info(
            format!(
                "Accepting TCP connection on port {} from {:?} sub_id: {:?}",
                port, &incoming_addr, target.sub_id
            ),
            vec![
                port.to_string(),
                incoming_addr.to_string(),
                format!("{:?}", target),
                format!("{:?}", target.sub_id),
            ],
        );
        let _ = stream.set_nodelay(true);
        if let Some(forward_handler) = forward_handler.as_ref() {
            let handler = forward_handler.clone();
            let cancel_clone = cancel.clone();
            let filters_clone = filters.clone();
            let out_ip_addr_clone = out_ip_addr.clone();
            tokio::spawn(async move {
                let _guard_move = guard;
                if let Err(e) = handler
                    .proc_tcp_stream(stream, cancel_clone, filters_clone, out_ip_addr_clone)
                    .await
                {
                    error!("TCP#{} <- {:?}: {:?}", port, &incoming_addr, e);
                }
            });
        } else {
            let server_selector_clone = server_selector.as_ref().unwrap().clone();
            if let Err(e) = direct_or_socks5_relay(
                stream,
                port,
                server_selector_clone,
                socks_backend.clone(),
                Some(collector_factory.clone()),
                cancel.clone(),
                incoming_addr,
                filters.clone(),
                out_ip_addr.clone(),
                guard,
            ) {
                error!(
                    "TCP#{} <- {:?}: start relay failed: {:?}",
                    port, &incoming_addr, e
                );
            }
        }
    }
}

fn direct_or_socks5_relay(
    client_stream: TcpStream,
    port: u16,
    server_selector: ServerSelector,
    socks_backend: Option<Arc<SocksBackend>>,
    collector_factory: Option<Arc<CollectorFactory>>,
    cancel: CancellationToken,
    incoming_addr: SocketAddr,
    filters: Option<Arc<Vec<FilterType>>>,
    out_ip_addr: Option<IpAddr>,
    guard: Option<Guard>,
) -> Result<()> {
    {
        let socket_ref = socket2::SockRef::from(&client_stream);
        #[cfg(target_os = "linux")]
        let _ = socket_ref.set_tcp_user_timeout(Some(std::time::Duration::from_secs(302)));
        let _ = socket_ref.set_tcp_keepalive(
            &TcpKeepalive::new()
                .with_time(std::time::Duration::from_secs(300))
                .with_interval(std::time::Duration::from_secs(2))
                .with_retries(1),
        );
    }
    let collector = collector_factory
        .unwrap()
        .create_collector_group(smallvec![None], port);
    if let Some(filters) = filters {
        let mut filtered_stream = TrafficFilter::new(client_stream);
        for filter in filters.iter() {
            filtered_stream.add_filter(filter.into());
        }
        tokio::spawn(start_tcp_relay(
            filtered_stream,
            port,
            server_selector,
            socks_backend,
            collector,
            cancel,
            incoming_addr,
            out_ip_addr,
            guard,
        ));
    } else {
        tokio::spawn(start_tcp_relay(
            client_stream,
            port,
            server_selector,
            socks_backend,
            collector,
            cancel,
            incoming_addr,
            out_ip_addr,
            guard,
        ));
    };
    Ok(())
}

pub async fn listen_udp(
    port: u16,
    bind_addr: Option<SocketAddr>,
    out_addr: Option<IpAddr>,
    target: Arc<TargetData>,
    socks_backend: Option<Arc<SocksBackend>>,
    forward_handler: Option<Arc<Box<dyn ProtocolHandler>>>,
    ping_client: Arc<PingClient>,
    cancel: CancellationToken,
    collector_factory: Arc<CollectorFactory>,
    #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
) -> Result<()> {
    // let map = ctx.tcp_port_to_target_map.clone();
    // let t = target.clone();
    let conn_limitter = create_conn_limitter(&target);
    if let Some(forward_handler) = forward_handler.as_ref() {
        forward_handler
            .proc_udp_bind(
                bind_addr,
                out_addr,
                cancel,
                #[cfg(feature = "audit_log")]
                log_client,
                target.sub_id,
                conn_limitter,
            )
            .await?;
    } else {
        let server_selector = create_server_selector_direct(
            target.as_ref().into(),
            target.mode,
            target.latency_test_method,
            cancel.clone(),
            ping_client,
        )
        .await?;
        let r = relay_udp_via_socks5_or_direct(
            port,
            bind_addr,
            out_addr,
            server_selector,
            &socks_backend,
            cancel,
            collector_factory,
            #[cfg(feature = "audit_log")]
            log_client,
            target.sub_id,
            conn_limitter,
        )
        .await;
        log::info!("udp relay rst: {r:?} port: {port}");
    }
    Ok(())
}
