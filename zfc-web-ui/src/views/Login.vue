<template>
  <div class="login-container">
    <div class="theme-toggle-wrapper">
      <ThemeToggle variant="button" />
    </div>
    <el-card class="login-card">
      <template #header>
        <h2>Port Forwarding Management</h2>
      </template>
      <el-form @submit.prevent="handleLogin">
        <el-form-item>
          <el-input
            v-model="token"
            placeholder="Enter your subscription token"
            :prefix-icon="Key"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" native-type="submit" :loading="loading" block>
            Login
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { Key } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ThemeToggle from '../components/ThemeToggle.vue'

const router = useRouter()
const authStore = useAuthStore()

const token = ref('')
const loading = ref(false)

const handleLogin = async () => {
  if (!token.value) {
    ElMessage.warning('Please enter your subscription token')
    return
  }

  loading.value = true
  try {
    await authStore.login(token.value)
    ElMessage.success('Login successful')
    router.push('/dashboard')
  } catch (error) {
    ElMessage.error(error.response?.data?.message || 'Login failed')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--theme-bg-secondary);
  position: relative;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-base);
}

.login-card :deep(.el-card__header) {
  text-align: center;
  background-color: var(--theme-card-bg);
  border-bottom: 1px solid var(--theme-border-base);
}

.login-card :deep(.el-card__header h2) {
  color: var(--theme-text-primary);
  margin: 0;
}

.login-card :deep(.el-card__body) {
  background-color: var(--theme-card-bg);
}

/* Theme toggle in login page */
.theme-toggle-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}
</style>