<template>
  <el-container class="dashboard">
    <el-header>
      <div class="header-content">
        <h2>Port Forwarding Management</h2>
        <div class="header-actions">
          <ThemeToggle variant="button" />
          <el-button type="danger" @click="handleLogout" :icon="SwitchButton">
            Logout
          </el-button>
        </div>
      </div>
    </el-header>
    
    <el-container>
      <el-menu
        class="sidebar"
        :router="true"
        :default-active="$route.path"
      >
        <el-menu-item index="/dashboard">
          <el-icon><Ticket /></el-icon>
          <span>Subscription</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/ports">
          <el-icon><Connection /></el-icon>
          <span>Ports</span>
        </el-menu-item>
        <el-menu-item v-if="subscriptionStore.allowForwardEndpoint" index="/dashboard/forward-endpoints">
          <el-icon><Position /></el-icon>
          <span>Forward Endpoints</span>
        </el-menu-item>
        <template v-if="subscriptionStore.isAdmin">
          <el-menu-item index="/dashboard/subscription-management">
            <el-icon><Setting /></el-icon>
            <span>Subscription Management</span>
          </el-menu-item>
          <el-menu-item index="/dashboard/server-management">
            <el-icon><Monitor /></el-icon>
            <span>Server Management</span>
          </el-menu-item>
        </template>
      </el-menu>
      
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useSubscriptionStore } from '../stores/subscription'
import { SwitchButton, Ticket, Connection, Position, Setting, Monitor } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, watch } from 'vue'
import ThemeToggle from '../components/ThemeToggle.vue'

const router = useRouter()
const authStore = useAuthStore()
const subscriptionStore = useSubscriptionStore()

// Watch for changes in admin status
watch(() => subscriptionStore.isAdmin, (isAdmin) => {
  // If user is not admin and tries to access admin pages, redirect to dashboard
  const currentPath = router.currentRoute.value.path
  if (!isAdmin && (
    currentPath.includes('subscription-management') || 
    currentPath.includes('server-management')
  )) {
    router.push('/dashboard')
    ElMessage.warning('You do not have permission to access this page')
  }
})

const handleLogout = () => {
  authStore.logout()
  subscriptionStore.clearData()
  ElMessage.success('Logged out successfully')
  router.push('/')
}

// Fetch subscription data when the dashboard mounts
onMounted(async () => {
  if (authStore.isAuthenticated) {
    await subscriptionStore.fetchData(true)
  }
})

// Watch for auth state changes
watch(() => authStore.isAuthenticated, (isAuthenticated) => {
  if (isAuthenticated) {
    subscriptionStore.fetchData()
  } else {
    subscriptionStore.clearData()
  }
})
</script>

<style scoped>
.dashboard {
  height: 100vh;
  background-color: var(--theme-bg-primary);
}

.el-header {
  background-color: var(--theme-header-bg);
  border-bottom: 1px solid var(--theme-border-base);
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  color: var(--theme-text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar {
  width: 220px;
  height: 100%;
  border-right: 1px solid var(--theme-border-base);
  background-color: var(--theme-sidebar-bg);
}

.el-main {
  padding: 20px;
  background-color: var(--theme-bg-secondary);
}

.el-menu-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 12px;
}

.el-icon {
  margin-right: 8px;
}

/* Dark theme specific adjustments */
:global(.dark) .sidebar :deep(.el-menu) {
  background-color: var(--theme-sidebar-bg);
  border-right: 1px solid var(--theme-border-base);
}

:global(.dark) .sidebar :deep(.el-menu-item) {
  color: var(--theme-text-regular);
}

:global(.dark) .sidebar :deep(.el-menu-item:hover) {
  background-color: var(--theme-fill-light);
}

:global(.dark) .sidebar :deep(.el-menu-item.is-active) {
  background-color: var(--theme-primary);
  color: #ffffff;
}
</style>