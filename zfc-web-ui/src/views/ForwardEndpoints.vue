<template>
  <div class="forward-endpoints-container">
    <div class="header">
      <h2>Forward Endpoint Management</h2>
      <div class="header-actions">
        <el-button
          @click="toggleSearch"
          :icon="searchExpanded ? ArrowUp : ArrowDown"
          type="default"
          class="search-toggle-btn"
          :aria-expanded="searchExpanded"
          :aria-controls="'search-form-container'"
          :title="searchExpanded ? 'Hide search filters' : 'Show search filters'"
        >
          <span class="search-toggle-text">
            {{ searchExpanded ? 'Hide Search' : 'Show Search' }}
          </span>
        </el-button>
        <el-button type="primary" @click="showDialog">Add Endpoint</el-button>
      </div>
    </div>

    <!-- Collapsible Search Form -->
    <el-collapse-transition>
      <div
        v-show="searchExpanded"
        class="search-container"
        id="search-form-container"
        role="region"
        aria-label="Search and filter options"
      >
        <el-form :model="searchForm" class="search-form" :inline="true">
          <el-form-item label="ID">
            <el-input
              v-model="searchForm.id"
              placeholder="Search by exact ID"
              clearable
              style="width: 120px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item label="Name">
            <el-input
              v-model="searchForm.name"
              placeholder="Search by name (regex supported)"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item label="Ingress Address">
            <el-input
              v-model="searchForm.ingressAddress"
              placeholder="Search ingress address (regex supported)"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              Search
            </el-button>
            <el-button @click="handleClearSearch">
              Clear
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>

    <!-- Forward Endpoints Table -->
    <el-table :data="endpoints" style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="Name" />
      <el-table-column prop="ingress_address" label="Ingress Address" />
      <el-table-column prop="protocol" label="Protocol" />
      <el-table-column prop="serve_port" label="Serve Port" />
      <el-table-column label="Actions" width="180">
        <template #default="{ row }">
          <el-tooltip
            content="Edit"
            placement="top"
            effect="light"
            manual
            :visible="tooltipVisibleMap.get(row.id + '_edit')"
            popper-class="custom-tooltip"
          >
            <el-button
              type="primary"
              :icon="Edit"
              circle
              @click="editEndpoint(row)"
              @mouseenter="() => handleMouseEnter(row, 'edit')"
              @mouseleave="() => handleMouseLeave(row, 'edit')"
            />
          </el-tooltip>
          <el-tooltip
            content="Copy Install Script"
            placement="top"
            effect="light"
            manual
            :visible="tooltipVisibleMap.get(row.id + '_copy')"
            popper-class="custom-tooltip"
          >
            <el-button
              type="success"
              :icon="DocumentCopy"
              circle
              @click="copyInstallScript(row)"
              @mouseenter="() => handleMouseEnter(row, 'copy')"
              @mouseleave="() => handleMouseLeave(row, 'copy')"
            />
          </el-tooltip>
          <el-tooltip
            content="Delete"
            placement="top"
            effect="light"
            manual
            :visible="tooltipVisibleMap.get(row.id + '_delete')"
            popper-class="custom-tooltip"
          >
            <el-button
              type="danger"
              :icon="Delete"
              circle
              @click="deleteEndpoint(row.id)"
              @mouseenter="() => handleMouseEnter(row, 'delete')"
              @mouseleave="() => handleMouseLeave(row, 'delete')"
            />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- Pagination Controls -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span>Total {{ totalItems }} items</span>
        <el-select
          v-model="pageSize"
          @change="handlePageSizeChange"
          class="page-size-selector"
          size="small"
        >
          <el-option
            v-for="size in pageSizeOptions"
            :key="size"
            :label="`${size} / page`"
            :value="size"
          />
        </el-select>
      </div>
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalItems"
        :page-count="totalPages"
        layout="prev, pager, next, jumper"
        @current-change="handlePageChange"
        class="pagination-controls"
        small
      />
    </div>

    <!-- Add/Edit Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? 'Edit Forward Endpoint' : 'Add Forward Endpoint'"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="endpoint-form"
      >
        <el-form-item label="Name" prop="name">
          <el-input v-model="form.name" placeholder="Enter name" />
        </el-form-item>
        
        <el-form-item label="IP / Domain" prop="ingress_address">
          <el-input v-model="form.ingress_address" placeholder="Enter IP address or domain name" />
        </el-form-item>

        <el-form-item label="Protocol" prop="protocol">
          <el-select v-model="form.protocol" placeholder="Select protocol" style="width: 100%">
            <el-option label="Hammer" value="Hammer" />
          </el-select>
        </el-form-item>
        <el-form-item label="Allow IPv6">
            <el-switch v-model="form.allow_ipv6" />
        </el-form-item>
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="advanced-options">
                <el-icon><Setting /></el-icon>
                Advanced Options
              </div>
            </template>

            <el-form-item label="Serve Port" prop="serve_port">
              <el-input
                v-model="form.serve_port"
                type="number"
                placeholder="Enter serve port (1-65535)"
                :min="1"
                :max="65535"
              />
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">Cancel</el-button>
          <el-button type="primary" @click="handleSubmit">
            {{ isEditing ? 'Save' : 'Add' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getForwardEndpoints, createForwardEndpoint, deleteForwardEndpoint, modifyForwardEndpoint } from '../api'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete, DocumentCopy, Setting, InfoFilled, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { API_HOST } from '../config'

const endpoints = ref([])
const dialogVisible = ref(false)
const isEditing = ref(false)
const loading = ref(false)
const tooltipVisibleMap = ref(new Map())
const mouseEnterTimer = ref(null)

// Pagination state
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const totalPages = ref(1)
const pageSizeOptions = [20, 50, 100, 200]

// Search state
const searchForm = ref({
  id: '',
  name: '',
  ingressAddress: ''
})
const searchTimeout = ref(null)

// Search UI state
const searchExpanded = ref(false)

const form = ref({
  name: '',
  ingress_address: '',
  protocol: 'Hammer',
  serve_port: null,
  allow_ipv6: false
})

const rules = {
  name: [
    { required: true, message: 'Please input name', trigger: 'blur' },
  ],
  ingress_address: [
    { required: true, message: 'Please input ingress address', trigger: 'blur' },
  ],
  protocol: [
    { required: true, message: 'Please select protocol', trigger: 'change' },
  ],
  serve_port: [
    { required: false, trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value) {
          const port = parseInt(value)
          if (isNaN(port) || port < 1 || port > 65535) {
            callback(new Error('Port must be between 1 and 65535'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
}

const formRef = ref(null)

const handleMouseEnter = (row, type) => {
  const key = row.id + '_' + type
  // 清除之前的定时器
  if (mouseEnterTimer.value) {
    clearTimeout(mouseEnterTimer.value)
  }
  // 设置新的定时器
  mouseEnterTimer.value = setTimeout(() => {
    tooltipVisibleMap.value.set(key, true)
  }, 500)
}

const handleMouseLeave = (row, type) => {
  const key = row.id + '_' + type
  // 清除定时器
  if (mouseEnterTimer.value) {
    clearTimeout(mouseEnterTimer.value)
  }
  tooltipVisibleMap.value.set(key, false)
}

const clearAllTooltips = () => {
  // 清除定时器
  if (mouseEnterTimer.value) {
    clearTimeout(mouseEnterTimer.value)
  }
  // 强制关闭所有 tooltip
  endpoints.value.forEach(endpoint => {
    tooltipVisibleMap.value.set(endpoint.id + '_edit', false)
    tooltipVisibleMap.value.set(endpoint.id + '_copy', false)
    tooltipVisibleMap.value.set(endpoint.id + '_delete', false)
  })
}

const fetchEndpoints = async (page = currentPage.value, size = pageSize.value) => {
  loading.value = true
  try {
    // Prepare search parameters
    const searchParams = {
      page,
      page_size: size
    }

    // Add search criteria if they exist
    if (searchForm.value.id?.trim()) {
      searchParams.id = parseInt(searchForm.value.id.trim())
    }
    if (searchForm.value.name?.trim()) {
      searchParams.name = searchForm.value.name.trim()
    }
    if (searchForm.value.ingressAddress?.trim()) {
      searchParams.ingress_address = searchForm.value.ingressAddress.trim()
    }

    const response = await getForwardEndpoints(searchParams)

    // Handle paginated response
    if (response.data.forward_endpoints && response.data.pagination) {
      // New paginated response format
      endpoints.value = response.data.forward_endpoints
      currentPage.value = response.data.pagination.current_page
      pageSize.value = response.data.pagination.page_size
      totalItems.value = response.data.pagination.total_items
      totalPages.value = response.data.pagination.total_pages
    } else if (response.data?.forward_endpoints) {
      // Fallback for old response format (if backend doesn't support pagination yet)
      endpoints.value = response.data.forward_endpoints
      totalItems.value = response.data.forward_endpoints.length
      totalPages.value = 1
    } else {
      endpoints.value = []
      totalItems.value = 0
      totalPages.value = 1
    }

    // 清除之前的所有 tooltip 状态
    tooltipVisibleMap.value.clear()

    // 只有当有数据时才初始化 tooltip 状态
    if (endpoints.value && endpoints.value.length > 0) {
      endpoints.value.forEach(endpoint => {
        if (endpoint && endpoint.id) {
          tooltipVisibleMap.value.set(endpoint.id + '_edit', false)
          tooltipVisibleMap.value.set(endpoint.id + '_copy', false)
          tooltipVisibleMap.value.set(endpoint.id + '_delete', false)
        }
      })
    }
  } catch (error) {
    console.error('Error fetching endpoints:', error)
    ElMessage.error(error.message || 'Failed to fetch endpoints')
    endpoints.value = [] // 确保在错误时设置为空数组
    totalItems.value = 0
    totalPages.value = 1
  } finally {
    loading.value = false
  }
}

const showDialog = () => {
  dialogVisible.value = true
  isEditing.value = false
  // 重置表单
  form.value = {
    name: '',
    ingress_address: '',
    protocol: 'Hammer',
    serve_port: null,
    allow_ipv6: false
  }
}

const editEndpoint = (endpoint) => {
  clearAllTooltips()
  form.value = { ...endpoint }
  isEditing.value = true
  dialogVisible.value = true
}

const deleteEndpoint = async (id) => {
  clearAllTooltips()
  try {
    await ElMessageBox.confirm(
      'Are you sure you want to delete this forward endpoint?',
      'Warning',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    await deleteForwardEndpoint(id)
    ElMessage.success('Forward endpoint deleted successfully')
    await fetchEndpoints(currentPage.value, pageSize.value)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message)
    }
  }
}

const copyInstallScript = (row) => {
  clearAllTooltips()
  const script = `bash <(curl -s ${API_HOST}/setup_script/${row.token_id})`
  navigator.clipboard.writeText(script).then(() => {
    ElMessage({
      message: 'Install script copied to clipboard',
      type: 'success'
    })
  }).catch(() => {
    ElMessage({
      message: 'Failed to copy install script',
      type: 'error'
    })
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 准备提交数据
    const formData = {
      name: form.value.name,
      ingress_address: form.value.ingress_address,
      protocol: form.value.protocol,
      allow_ipv6: form.value.allow_ipv6
    }

    // 只在新增时添加端口信息
    // if (!isEditing.value) {
      if (form.value.serve_port) {
        formData.serve_port = parseInt(form.value.serve_port)
      }
    // }

    if (isEditing.value) {
      formData.id = form.value.id
      await modifyForwardEndpoint(formData)
      ElMessage.success('Forward endpoint updated successfully')
    } else {
      await createForwardEndpoint(formData)
      ElMessage.success('Forward endpoint created successfully')
    }
    
    handleDialogClose()
    fetchEndpoints(currentPage.value, pageSize.value)
  } catch (error) {
    console.error('Form validation or submission error:', error)
    ElMessage.error(error.message || 'Failed to submit form')
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  form.value = {
    name: '',
    ingress_address: '',
    protocol: 'Hammer',
    serve_port: null
  }
}

// Pagination handlers
const handlePageChange = (page) => {
  currentPage.value = page
  fetchEndpoints(page, pageSize.value)
}

const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // Reset to first page when changing page size
  fetchEndpoints(1, size)
}

// Search handlers
const toggleSearch = () => {
  searchExpanded.value = !searchExpanded.value
  // Save search expanded state to localStorage
  localStorage.setItem('forward-endpoint-search-expanded', JSON.stringify(searchExpanded.value))
}

// Load search expanded state from localStorage on component mount
const loadSearchExpandedState = () => {
  const saved = localStorage.getItem('forward-endpoint-search-expanded')
  if (saved !== null) {
    searchExpanded.value = JSON.parse(saved)
  }
}

const handleSearchInput = () => {
  // Clear existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }

  // Set new timeout for debounced search
  searchTimeout.value = setTimeout(() => {
    handleSearch()
  }, 500) // 500ms delay
}

const handleSearch = () => {
  currentPage.value = 1 // Reset to first page when searching
  fetchEndpoints(1, pageSize.value)
}

const handleClearSearch = () => {
  searchForm.value = {
    id: '',
    name: '',
    ingressAddress: ''
  }
  handleSearch()
}

onMounted(() => {
  loadSearchExpandedState()
  fetchEndpoints()
})

onUnmounted(() => {
  tooltipVisibleMap.value.clear()
  if (mouseEnterTimer.value) {
    clearTimeout(mouseEnterTimer.value)
  }
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
})
</script>

<style scoped>
.forward-endpoints {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Search Toggle Button Styles */
.search-toggle-btn {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
}

.search-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-toggle-text {
  margin-left: 4px;
  margin-right: 4px;
}

/* Search Container Styles */
.search-container {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

/* Dark mode search container */
.dark .search-container {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:root.dark .search-toggle-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.advanced-options {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
}

.advanced-options .el-icon {
  margin-right: 4px;
}

:deep(.el-collapse) {
  border: none;
  margin: 16px 0;
}

:deep(.el-collapse-item__header) {
  border: none;
  font-size: 14px;
  color: var(--el-color-primary);
}

:deep(.el-collapse-item__wrap) {
  border: none;
}

:deep(.el-collapse-item__content) {
  padding-top: 16px;
}

.port-section {
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-lighter);
  margin-top: 8px;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.page-size-selector {
  width: 120px;
}

.pagination-controls {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .search-toggle-btn {
    width: 100%;
    justify-content: center;
  }

  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
  }
}

.port-section-header {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-text-color-regular);
  font-size: 14px;
  margin-bottom: 12px;
  padding: 0 2px;
}

.port-section-header .el-icon {
  font-size: 16px;
  color: var(--el-color-info);
}

.port-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 2px;
}

.port-item {
  display: flex;
  align-items: center;
}

.port-label {
  width: 120px;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.port-value {
  flex: 1;
  color: var(--el-text-color-primary);
  font-family: var(--el-font-family-monospace);
  font-size: 14px;
}
</style>
